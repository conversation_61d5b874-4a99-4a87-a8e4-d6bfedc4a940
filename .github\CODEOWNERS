# GitOps Code Ownership and Approval Requirements
# This file defines who must approve changes to specific parts of the GitOps repository
# 
# Documentation: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# Global fallback - repository administrators
* @AshrafSyed25

# =============================================================================
# PRODUCTION ENVIRONMENT PROTECTION
# =============================================================================
# All production deployment files require approval from senior team members
# This includes manifests, configurations, and any production-related changes

# Production overlay directories for all projects
deployments/*/overlays/production/ @AshrafSyed25
deployments/*/overlays/production/* @AshrafSyed25

# =============================================================================
# STAGING ENVIRONMENT PROTECTION
# =============================================================================
# Staging environment requires approval for quality assurance
# This ensures staging deployments are reviewed before production promotion

# Staging overlay directories for all projects
deployments/*/overlays/staging/ @AshrafSyed25
deployments/*/overlays/staging/* @AshrafSyed25




# This CODEOWNERS file itself
.github/CODEOWNERS @AshrafSyed25


