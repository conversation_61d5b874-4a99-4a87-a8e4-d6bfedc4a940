apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ai-spring-backend-test

resources:
- deployment.yaml
- service.yaml
- configmap.yaml

labels:
- pairs:
    app: ai-spring-backend-test
    app.kubernetes.io/name: ai-spring-backend-test
    app.kubernetes.io/part-of: ai-spring-backend-test
    app.kubernetes.io/managed-by: argocd
    source.repo: ChidhagniConsulting-ai-spring-backend
    source.branch: 25-merge

commonAnnotations:
  app.kubernetes.io/managed-by: kustomize
  source.commit: c130a114

namePrefix: ""
nameSuffix: ""

images:
- name: registry.digitalocean.com/doks-registry/ai-spring-backend:latest
  newName: registry.digitalocean.com/doks-registry/ai-spring-backend:latest

replicas:
- name: ai-spring-backend-test
  count: 1
