name: 🚀 Deploy from CI/CD Pipeline

on:
  repository_dispatch:
    types: [deploy-to-argocd]

env:
  GITOPS_TOKEN: ${{ secrets.GITOPS_TOKEN }}
  DIGITALOCEAN_ACCESS_TOKEN: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}

jobs:
  validate-dispatch:
    runs-on: [self-hosted, Linux]
    outputs:
      should-deploy: ${{ steps.validate.outputs.should-deploy }}
      app-name: ${{ steps.validate.outputs.app-name }}
      project-id: ${{ steps.validate.outputs.project-id }}
      application-type: ${{ steps.validate.outputs.application-type }}
      environment: ${{ steps.validate.outputs.environment }}
      docker-image: ${{ steps.validate.outputs.docker-image }}
      docker-tag: ${{ steps.validate.outputs.docker-tag }}
      container-port: ${{ steps.validate.outputs.container-port }}
      health-check-path: ${{ steps.validate.outputs.health-check-path }}
      backend-type: ${{ steps.validate.outputs.backend-type }}
      source-repo: ${{ steps.validate.outputs.source-repo }}
      source-branch: ${{ steps.validate.outputs.source-branch }}
      commit-sha: ${{ steps.validate.outputs.commit-sha }}
      secrets-encoded: ${{ steps.validate.outputs.secrets-encoded }}
    steps:
      - name: 🔍 Validate Dispatch Payload
        id: validate
        run: |
          echo "=== REPOSITORY DISPATCH VALIDATION ==="
          echo "Event type: ${{ github.event.action }}"
          echo "Repository: ${{ github.repository }}"
          echo "Triggered by: ${{ github.actor }}"
          echo "Workflow run ID: ${{ github.run_id }}"
          echo "Client payload: ${{ toJson(github.event.client_payload) }}"
          echo "=================================="

          # Extract required payload data
          APP_NAME="${{ github.event.client_payload.app_name }}"
          PROJECT_ID="${{ github.event.client_payload.project_id }}"
          ENVIRONMENT="${{ github.event.client_payload.environment }}"
          DOCKER_IMAGE="${{ github.event.client_payload.docker_image }}"
          DOCKER_TAG="${{ github.event.client_payload.docker_tag }}"

          # Extract optional payload data with defaults
          APPLICATION_TYPE="${{ github.event.client_payload.application_type }}"
          CONTAINER_PORT="${{ github.event.client_payload.container_port }}"
          BACKEND_TYPE="${{ github.event.client_payload.backend_type }}"
          # Note: HEALTH_CHECK_PATH is automatically set based on APPLICATION_TYPE below

          # Extract tracking data (optional)
          SOURCE_REPO="${{ github.event.client_payload.source_repo }}"
          SOURCE_BRANCH="${{ github.event.client_payload.source_branch }}"
          COMMIT_SHA="${{ github.event.client_payload.commit_sha }}"

          # Extract secrets payload (optional)
          SECRETS_ENCODED="${{ github.event.client_payload.secrets_encoded }}"

          # Validate required fields
          if [ -z "$APP_NAME" ] || [ -z "$PROJECT_ID" ] || [ -z "$ENVIRONMENT" ] || [ -z "$DOCKER_IMAGE" ] || [ -z "$DOCKER_TAG" ]; then
            echo "❌ Missing required fields in dispatch payload"
            echo "Required: app_name, project_id, environment, docker_image, docker_tag"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Set default application type if not provided
          if [ -z "$APPLICATION_TYPE" ]; then
            APPLICATION_TYPE="web-app"
            echo "⚠️ No application_type provided, defaulting to: $APPLICATION_TYPE"
          fi

          # Validate project ID format (lowercase alphanumeric with hyphens)
          if ! echo "$PROJECT_ID" | grep -qE '^[a-z0-9-]+$'; then
            echo "❌ Invalid project ID format: $PROJECT_ID"
            echo "Project ID must be lowercase alphanumeric with hyphens only"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate environment
          if [ "$ENVIRONMENT" != "dev" ] && [ "$ENVIRONMENT" != "staging" ] && [ "$ENVIRONMENT" != "production" ]; then
            echo "❌ Invalid environment: $ENVIRONMENT"
            echo "Supported environments: dev, staging, production"
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Validate deployment flow sequence
          # Note: This validation is informational - the actual flow control is handled by the promotion jobs
          if [ "$ENVIRONMENT" = "staging" ]; then
            echo "ℹ️ Staging deployment detected"
            echo "   This should typically be triggered via dev → staging promotion"
            echo "   Ensure this is an approved staging deployment"
          elif [ "$ENVIRONMENT" = "production" ]; then
            echo "ℹ️ Production deployment detected"
            echo "   This should typically be triggered via staging → production promotion"
            echo "   Ensure this is an approved production deployment"
          fi

          # Validate application type
          case "$APPLICATION_TYPE" in
            "react-frontend"|"springboot-backend"|"django-backend"|"nest-backend"|"web-app"|"api"|"microservice"|"worker"|"database")
              echo "✅ Valid application type: $APPLICATION_TYPE"
              ;;
            *)
              echo "❌ Invalid application type: $APPLICATION_TYPE"
              echo "Supported types: react-frontend, springboot-backend, django-backend, nest-backend, web-app, api, microservice, worker, database"
              echo "should-deploy=false" >> $GITHUB_OUTPUT
              exit 0
              ;;
          esac

          # Set type-specific defaults (automatically based on application type)
          case "$APPLICATION_TYPE" in
            "react-frontend")
              CONTAINER_PORT="${CONTAINER_PORT:-3000}"  # React apps typically run on port 3000
              HEALTH_CHECK_PATH="/"  # Always use / for React frontends
              # Set default backend type for react-frontend if not provided
              BACKEND_TYPE="${BACKEND_TYPE:-spring}"
              ;;
            "springboot-backend")
              CONTAINER_PORT="${CONTAINER_PORT:-8080}"
              HEALTH_CHECK_PATH="/actuator/health"  # Always use /actuator/health for Spring Boot
              ;;
            "django-backend")
              CONTAINER_PORT="${CONTAINER_PORT:-8000}"  # Django apps typically run on port 8000
              HEALTH_CHECK_PATH="/health/"  # Django health check endpoint
              ;;
            "nest-backend")
              CONTAINER_PORT="${CONTAINER_PORT:-3000}"  # NestJS apps typically run on port 3000
              HEALTH_CHECK_PATH="/health"  # NestJS health check endpoint
              ;;
            *)
              CONTAINER_PORT="${CONTAINER_PORT:-8080}"
              HEALTH_CHECK_PATH="/health"  # Default for other types
              ;;
          esac

          # Validate deployment sequence (non-blocking)
          echo "🔍 Validating deployment sequence..."
          if [ -f "scripts/validate-deployment-sequence.py" ]; then
            PAYLOAD_JSON=$(cat << EOF
          {
            "project_id": "$PROJECT_ID",
            "environment": "$ENVIRONMENT",
            "docker_tag": "$DOCKER_TAG"
          }
          EOF
            )

            python3 scripts/validate-deployment-sequence.py \
              --project-id "$PROJECT_ID" \
              --environment "$ENVIRONMENT" \
              --payload "$PAYLOAD_JSON" || echo "⚠️ Sequence validation had warnings"
          else
            echo "⚠️ Sequence validation script not found - skipping"
          fi

          echo "✅ Dispatch payload validation passed"
          echo "should-deploy=true" >> $GITHUB_OUTPUT
          echo "app-name=$APP_NAME" >> $GITHUB_OUTPUT
          echo "project-id=$PROJECT_ID" >> $GITHUB_OUTPUT
          echo "application-type=$APPLICATION_TYPE" >> $GITHUB_OUTPUT
          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "docker-image=$DOCKER_IMAGE" >> $GITHUB_OUTPUT
          echo "docker-tag=$DOCKER_TAG" >> $GITHUB_OUTPUT
          echo "container-port=$CONTAINER_PORT" >> $GITHUB_OUTPUT
          echo "health-check-path=$HEALTH_CHECK_PATH" >> $GITHUB_OUTPUT
          echo "backend-type=$BACKEND_TYPE" >> $GITHUB_OUTPUT
          echo "source-repo=$SOURCE_REPO" >> $GITHUB_OUTPUT
          echo "source-branch=$SOURCE_BRANCH" >> $GITHUB_OUTPUT
          echo "commit-sha=$COMMIT_SHA" >> $GITHUB_OUTPUT
          echo "secrets-encoded=$SECRETS_ENCODED" >> $GITHUB_OUTPUT

  generate-manifests:
    needs: validate-dispatch
    if: needs.validate-dispatch.outputs.should-deploy == 'true'
    runs-on: [self-hosted, Linux]
    outputs:
      generation-success: ${{ steps.generate.outputs.success }}
      project-path: ${{ steps.generate.outputs.project-path }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🔧 Setup kubectl
        shell: bash
        run: |
          # Check if kubectl is available
          if ! command -v kubectl &> /dev/null; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
            kubectl version --client
          fi

      - name: 🚀 Deploy with Dynamic Payload Processing
        id: generate
        shell: bash
        run: |
          echo "🚀 Using new dynamic payload processing system..."

          # Create payload JSON for the new deployment system
          PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "${{ needs.validate-dispatch.outputs.app-name }}",
            "project_id": "${{ needs.validate-dispatch.outputs.project-id }}",
            "application_type": "${{ needs.validate-dispatch.outputs.application-type }}",
            "environment": "${{ needs.validate-dispatch.outputs.environment }}",
            "docker_image": "${{ needs.validate-dispatch.outputs.docker-image }}",
            "docker_tag": "${{ needs.validate-dispatch.outputs.docker-tag }}",
            "source_repo": "${{ needs.validate-dispatch.outputs.source-repo }}",
            "source_branch": "${{ needs.validate-dispatch.outputs.source-branch }}",
            "commit_sha": "${{ needs.validate-dispatch.outputs.commit-sha }}",
            "secrets_encoded": "${{ needs.validate-dispatch.outputs.secrets-encoded }}"
          }
          EOF
          )

          echo "📦 Deployment payload:"
          echo "$PAYLOAD" | jq .

          # Validate payload has required fields
          if [ -z "$(echo "$PAYLOAD" | jq -r '.project_id')" ] || [ "$(echo "$PAYLOAD" | jq -r '.project_id')" = "null" ]; then
            echo "❌ Missing or invalid project_id in payload"
            exit 1
          fi

          if [ -z "$(echo "$PAYLOAD" | jq -r '.docker_image')" ] || [ "$(echo "$PAYLOAD" | jq -r '.docker_image')" = "null" ]; then
            echo "❌ Missing or invalid docker_image in payload"
            exit 1
          fi

          echo "🐍 Checking Python environment..."
          python3 --version || python --version

          echo "📦 Installing Python dependencies..."
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic processing"

          if [ ! -f "scripts/deploy.py" ]; then
            echo "❌ deploy.py script not found"
            exit 1
          fi

          echo "✅ deploy.py script found"

          python3 -c "import json, subprocess, argparse, tempfile, pathlib" || {
            echo "❌ Required Python modules not available"
            exit 1
          }

          echo "✅ Required Python modules available"

          SECRETS_IN_PAYLOAD=$(echo "$PAYLOAD" | jq -r '.secrets_encoded // empty')
          if [ -n "$SECRETS_IN_PAYLOAD" ] && [ "$SECRETS_IN_PAYLOAD" != "null" ]; then
            echo "🔐 Using secrets from payload"
          else
            echo "🔓 No secrets provided in payload"
          fi

          echo "🚀 Running deployment script..."

          # Extract project_id from payload
          PROJECT_ID=$(echo "$PAYLOAD" | jq -r '.project_id')
          echo "📋 Project ID: $PROJECT_ID"

          # Use manifests directory as template source
          TEMPLATE_DIR="manifests"

          # Check if template directory exists
          if [ ! -d "$TEMPLATE_DIR" ]; then
            echo "❌ Template directory not found: $TEMPLATE_DIR"
            echo "Available directories:"
            ls -la . || echo "Current directory listing failed"
            exit 1
          fi

          echo "✅ Using template directory: $TEMPLATE_DIR"
          echo "📁 Will create project directory: deployments/$PROJECT_ID"

          python3 scripts/deploy.py \
            --payload "$PAYLOAD" \
            --manifest-dir "$TEMPLATE_DIR" \
            --output-dir "generated-manifests" \
            --skip-validation \
            --dry-run

          status=$?
          if [ $status -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            PROJECT_PATH="deployments/$PROJECT_ID"
            echo "project-path=$PROJECT_PATH" >> $GITHUB_OUTPUT
            echo "✅ Manifest generation completed successfully"
            if [ ! -d "$PROJECT_PATH" ]; then
              echo "⚠️ Generated directory $PROJECT_PATH not found"
              echo "Available directories in deployments:"
              ls -la deployments/ || echo "deployments/ directory not found or empty"
              exit 1
            fi
            echo "📁 Generated directory structure:"
            find "$PROJECT_PATH" -type f | sort
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Manifest generation failed with exit code: $status"
            echo "🔍 Debugging information:"
            echo "   Working directory: $(pwd)"
            echo "   Python version: $(python3 --version 2>&1 || python --version 2>&1)"
            ls -la scripts/ || echo "   scripts/ directory not found"
            if [ -f "deploy.log" ]; then
              echo "   Last 10 lines of deploy.log:"
              tail -10 deploy.log
            fi
            exit 1
          fi

      - name: 📁 List Generated Files
        if: steps.generate.outputs.success == 'true'
        shell: bash
        run: |
          PROJECT_PATH="${{ steps.generate.outputs.project-path }}"
          if [ -d "$PROJECT_PATH" ]; then
            echo "📁 Generated project structure:"
            tree "$PROJECT_PATH" || find "$PROJECT_PATH" -type f | sort
          else
            echo "❌ Directory $PROJECT_PATH does not exist"
            exit 1
          fi

      - name: 🔧 Configure Git
        if: steps.generate.outputs.success == 'true'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

      - name: 💾 Commit Generated Files
        id: commit
        if: steps.generate.outputs.success == 'true'
        run: |
          git add "${{ steps.generate.outputs.project-path }}/"
          if git diff --staged --quiet; then
            echo "No changes to commit"
            echo "committed=false" >> $GITHUB_OUTPUT
          else
            git commit -m "🚀 Deploy ${{ needs.validate-dispatch.outputs.app-name }} (${{ needs.validate-dispatch.outputs.project-id }}) to ${{ needs.validate-dispatch.outputs.environment }} from CI/CD

            Triggered by merge to main in ${{ needs.validate-dispatch.outputs.source-repo }}

            - Application Type: ${{ needs.validate-dispatch.outputs.application-type }}
            - Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}
            - Container Port: ${{ needs.validate-dispatch.outputs.container-port }}
            - Health Check: ${{ needs.validate-dispatch.outputs.health-check-path }}
            - Environment: ${{ needs.validate-dispatch.outputs.environment }}
            - Source Commit: ${{ needs.validate-dispatch.outputs.commit-sha }}
            - ArgoCD Application and Project manifests
            - Complete Kubernetes deployment manifests

            Auto-generated by GitOps CI/CD integration"
            echo "committed=true" >> $GITHUB_OUTPUT
            echo "✅ Changes committed successfully"
          fi

      - name: 🚀 Push Changes
        if: steps.commit.outputs.committed == 'true'
        run: |
          git push origin main
          echo "✅ Changes pushed to main branch"

  deploy-to-argocd:
    needs: [validate-dispatch, generate-manifests]
    if: needs.generate-manifests.outputs.generation-success == 'true'
    runs-on: [self-hosted, Linux]
    outputs:
      deployment-success: ${{ steps.auto-deploy.outputs.deployment-success }}
      environment: ${{ needs.validate-dispatch.outputs.environment }}
      project-id: ${{ needs.validate-dispatch.outputs.project-id }}
      app-name: ${{ needs.validate-dispatch.outputs.app-name }}
      docker-image: ${{ needs.validate-dispatch.outputs.docker-image }}
      docker-tag: ${{ needs.validate-dispatch.outputs.docker-tag }}
      application-type: ${{ needs.validate-dispatch.outputs.application-type }}
      source-repo: ${{ needs.validate-dispatch.outputs.source-repo }}
      source-branch: ${{ needs.validate-dispatch.outputs.source-branch }}
      commit-sha: ${{ needs.validate-dispatch.outputs.commit-sha }}
      secrets-encoded: ${{ needs.validate-dispatch.outputs.secrets-encoded }}
    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITOPS_TOKEN }}
          fetch-depth: 0

      - name: 🔧 Setup kubectl for ArgoCD Management Cluster
        id: setup-kubectl
        continue-on-error: true
        run: |
          echo "🔧 Setting up kubectl for ArgoCD Management cluster..."

          # Check if auto-deployment is enabled
          AUTO_DEPLOY_ENABLED="${{ vars.ENABLE_AUTO_DEPLOY }}"
          if [ "$AUTO_DEPLOY_ENABLED" != "true" ]; then
            echo "auto-deploy-enabled=false" >> $GITHUB_OUTPUT
            echo "ℹ️ Auto-deployment is disabled (ENABLE_AUTO_DEPLOY != true)"
            exit 0
          fi
          echo "auto-deploy-enabled=true" >> $GITHUB_OUTPUT

          # Check if DIGITALOCEAN_ACCESS_TOKEN is set
          if [ -z "$DIGITALOCEAN_ACCESS_TOKEN" ]; then
            echo "❌ DIGITALOCEAN_ACCESS_TOKEN is not set"
            echo "Please add your DigitalOcean access token as a repository secret"
            echo "auto-deploy-enabled=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Install doctl if not available
          if ! command -v doctl >/dev/null 2>&1; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget -q "https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz"
            tar xf "doctl-1.104.0-linux-amd64.tar.gz"
            sudo mv doctl /usr/local/bin/
            sudo chmod +x /usr/local/bin/doctl
            rm -f "doctl-1.104.0-linux-amd64.tar.gz"
            echo "✅ doctl installed successfully"
          else
            echo "✅ doctl is already available"
          fi

          # Install kubectl if not available
          if ! command -v kubectl >/dev/null 2>&1; then
            echo "📦 Installing kubectl..."
            curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
            chmod +x kubectl
            sudo mv kubectl /usr/local/bin/
            echo "✅ kubectl installed successfully"
          else
            echo "✅ kubectl is already available"
          fi

          echo "kubectl-available=true" >> $GITHUB_OUTPUT

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          if ! doctl auth init --access-token "$DIGITALOCEAN_ACCESS_TOKEN"; then
            echo "❌ Failed to authenticate with DigitalOcean"
            echo "Please check DIGITALOCEAN_ACCESS_TOKEN secret"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Configure kubectl for ArgoCD Management Cluster
          # ArgoCD always runs on the management cluster regardless of target environment
          CLUSTER_ID="158b6a47-3e7e-4dca-af0f-05a6e07115af"
          ENVIRONMENT="${{ needs.validate-dispatch.outputs.environment }}"
          echo "⚙️  Configuring kubectl for ArgoCD Management cluster: $CLUSTER_ID"
          echo "🎯 Target environment: $ENVIRONMENT (applications will be deployed to target cluster via ArgoCD)"

          # Try to configure kubectl for the cluster
          if ! doctl kubernetes cluster kubeconfig save "$CLUSTER_ID"; then
            echo "❌ Failed to configure kubectl for ArgoCD Management cluster"
            echo "This could be due to:"
            echo "  - Network connectivity issues"
            echo "  - Invalid cluster ID"
            echo "  - Insufficient permissions"
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Test cluster connectivity with detailed error reporting
          echo "🔍 Testing cluster connectivity..."
          if kubectl cluster-info >/dev/null 2>&1; then
            echo "cluster-accessible=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD Management cluster is accessible"
            kubectl cluster-info
          else
            echo "cluster-accessible=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD Management cluster is not accessible"
            echo ""
            echo "🔍 Connectivity test failed. This could be due to:"
            echo "  - DNS resolution issues (cluster hostname not resolvable)"
            echo "  - Network firewall blocking access"
            echo "  - Cluster is down or unavailable"
            echo "  - Self-hosted runner network configuration"
            echo ""
            echo "💡 Troubleshooting steps:"
            echo "  1. Check if the runner can resolve: $CLUSTER_ID.k8s.ondigitalocean.com"
            echo "  2. Verify the cluster is running in DigitalOcean console"
            echo "  3. Check runner network connectivity and firewall rules"
            echo "  4. Ensure DIGITALOCEAN_ACCESS_TOKEN has cluster access permissions"
            exit 0
          fi

          # Check ArgoCD availability
          if kubectl get namespace argocd >/dev/null 2>&1; then
            echo "argocd-available=true" >> $GITHUB_OUTPUT
            echo "✅ ArgoCD namespace exists"
          else
            echo "argocd-available=false" >> $GITHUB_OUTPUT
            echo "❌ ArgoCD namespace not found"
            exit 0
          fi

          echo "✅ All prerequisites met for auto-deployment"

      - name: 🚀 Auto-Deploy to ArgoCD
        id: auto-deploy
        if: steps.setup-kubectl.outputs.auto-deploy-enabled == 'true' && steps.setup-kubectl.outputs.cluster-accessible == 'true' && steps.setup-kubectl.outputs.argocd-available == 'true'
        continue-on-error: true
        run: |
          PROJECT_ID="${{ needs.validate-dispatch.outputs.project-id }}"
          APP_NAME="${{ needs.validate-dispatch.outputs.app-name }}"
          ENVIRONMENT="${{ needs.validate-dispatch.outputs.environment }}"
          DOCKER_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}"
          DOCKER_TAG="${{ needs.validate-dispatch.outputs.docker-tag }}"

          echo "🚀 Starting automated ArgoCD deployment for: $PROJECT_ID"
          echo "📋 Application: $APP_NAME"
          echo "🌍 Environment: $ENVIRONMENT"
          echo "🐳 Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"

          # Initialize deployment status
          DEPLOYMENT_SUCCESS=false
          DEPLOYMENT_ERROR=""

          # Pull latest changes to ensure we have the generated manifests
          git pull origin main

          # Verify manifest files exist
          PROJECT_FILE="deployments/$PROJECT_ID/argocd/project.yaml"
          APPLICATION_FILE="deployments/$PROJECT_ID/overlays/$ENVIRONMENT/application.yaml"

          if [ ! -f "$PROJECT_FILE" ] || [ ! -f "$APPLICATION_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD manifest files not found: $PROJECT_FILE, $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Manifest files found"
          echo "📋 Project file: $PROJECT_FILE"
          echo "🎯 Application file: $APPLICATION_FILE"

          # Install PyYAML if not available
          echo "🐍 Ensuring Python dependencies..."
          pip3 install PyYAML --user --quiet || echo "⚠️ PyYAML installation failed, will use basic validation"

          # Validate YAML syntax using Python (offline validation)
          echo "🔍 Validating YAML syntax (offline)..."
          python3 scripts/validate-yaml.py "$PROJECT_FILE" "$APPLICATION_FILE"
          if [ $? -ne 0 ]; then
            DEPLOYMENT_ERROR="YAML validation failed for ArgoCD manifests"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          echo "✅ Manifest files validated successfully (offline)"

          # Additional kubectl validation only if cluster is accessible
          if [ "${{ steps.setup-kubectl.outputs.cluster-accessible }}" = "true" ]; then
            echo "🔍 Performing kubectl validation against cluster..."
            if kubectl apply --dry-run=client --validate=true -f "$PROJECT_FILE" -f "$APPLICATION_FILE" >/dev/null 2>&1; then
              echo "✅ Kubectl validation passed"
            else
              echo "⚠️ Kubectl validation failed, but continuing with deployment"
              echo "This may be due to missing CRDs or network issues"
            fi
          else
            echo "⚠️ Skipping kubectl validation (cluster not accessible)"
          fi

          # Step 2: Apply ArgoCD Project
          echo "📋 Applying ArgoCD Project..."
          echo "🔍 Project file: $PROJECT_FILE"

          # First, validate the project file exists and is readable
          if [ ! -f "$PROJECT_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD Project file not found: $PROJECT_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Try to apply the project with detailed error reporting
          if kubectl apply -f "$PROJECT_FILE" 2>&1; then
            echo "✅ ArgoCD Project applied successfully"
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Project: $PROJECT_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo ""
            echo "🔍 This could be due to:"
            echo "  - Invalid YAML syntax in project file"
            echo "  - ArgoCD CRDs not installed in the cluster"
            echo "  - Insufficient RBAC permissions"
            echo "  - Network connectivity issues"
            echo ""
            echo "💡 Manual troubleshooting:"
            echo "  kubectl apply --dry-run=client -f $PROJECT_FILE"
            echo "  kubectl get crd appprojects.argoproj.io"
            echo "  kubectl auth can-i create appprojects.argoproj.io -n argocd"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 3: Apply ArgoCD Application
          echo "🎯 Applying ArgoCD Application..."
          echo "🔍 Application file: $APPLICATION_FILE"

          # First, validate the application file exists and is readable
          if [ ! -f "$APPLICATION_FILE" ]; then
            DEPLOYMENT_ERROR="ArgoCD Application file not found: $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Try to apply the application with detailed error reporting
          if kubectl apply -f "$APPLICATION_FILE" 2>&1; then
            echo "✅ ArgoCD Application applied successfully"
            DEPLOYMENT_SUCCESS=true
          else
            DEPLOYMENT_ERROR="Failed to apply ArgoCD Application: $APPLICATION_FILE"
            echo "❌ $DEPLOYMENT_ERROR"
            echo ""
            echo "🔍 This could be due to:"
            echo "  - Invalid YAML syntax in application file"
            echo "  - Referenced ArgoCD Project does not exist"
            echo "  - Invalid source repository or path"
            echo "  - Invalid destination cluster or namespace"
            echo ""
            echo "💡 Manual troubleshooting:"
            echo "  kubectl apply --dry-run=client -f $APPLICATION_FILE"
            echo "  kubectl get appproject $PROJECT_ID-project -n argocd"
            echo "  kubectl auth can-i create applications.argoproj.io -n argocd"
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
            exit 0
          fi

          # Step 4: Wait for application to be created in ArgoCD
          echo "⏳ Waiting for ArgoCD application to be created..."
          for i in {1..30}; do
            if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
              echo "✅ ArgoCD application '$PROJECT_ID' created successfully"
              break
            fi
            echo "⏳ Waiting for application creation... ($i/30)"
            sleep 2
          done

          # Step 5: Trigger sync if application exists
          if kubectl get application "$PROJECT_ID" -n argocd >/dev/null 2>&1; then
            echo "🔄 Triggering ArgoCD application sync..."
            if kubectl patch application "$PROJECT_ID" -n argocd --type merge -p '{"operation":{"sync":{}}}' 2>/dev/null || true; then
              echo "✅ ArgoCD sync triggered"
            else
              echo "ℹ️ Manual sync may be required in ArgoCD dashboard"
            fi
          fi

          if [ "$DEPLOYMENT_SUCCESS" = true ]; then
            echo "deployment-success=true" >> $GITHUB_OUTPUT
            echo "🎉 ArgoCD deployment completed successfully!"
            echo ""
            echo "📊 Deployment Summary:"
            echo "  • Project: $PROJECT_ID"
            echo "  • Application: $APP_NAME"
            echo "  • Environment: $ENVIRONMENT"
            echo "  • Docker Image: $DOCKER_IMAGE:$DOCKER_TAG"
            echo "  • ArgoCD Dashboard: Check your ArgoCD UI for deployment status"
          else
            echo "deployment-success=false" >> $GITHUB_OUTPUT
            echo "deployment-error=$DEPLOYMENT_ERROR" >> $GITHUB_OUTPUT
          fi

      - name: 🎉 Success Notification
        if: steps.auto-deploy.outputs.deployment-success == 'true'
        run: |
          echo "🎉 CI/CD-triggered deployment completed successfully!"
          echo ""
          echo "📊 Deployment Details:"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Project ID: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo "  • Source Repository: ${{ needs.validate-dispatch.outputs.source-repo }}"
          echo "  • Source Branch: ${{ needs.validate-dispatch.outputs.source-branch }}"
          echo "  • Commit SHA: ${{ needs.validate-dispatch.outputs.commit-sha }}"
          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment in ArgoCD dashboard"
          echo "  • Verify application health in Kubernetes cluster"
          echo "  • Check application logs if needed"

      - name: ⚠️ Manual Deployment Required
        if: steps.fallback-instructions.outputs.deployment-success == 'manual-required'
        run: |
          echo "⚠️ Automatic deployment was skipped due to connectivity issues"
          echo ""
          echo "📊 Deployment Details:"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Project ID: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo ""
          echo "✅ Manifest files have been generated and validated"
          echo "🔧 Manual deployment is required - see job logs for instructions"

      - name: ❌ Deployment Failed
        if: failure() || (steps.setup-kubectl.outputs.auto-deploy-enabled == 'true' && steps.auto-deploy.outputs.deployment-success != 'true')
        run: |
          echo "❌ CI/CD-triggered deployment failed"
          echo ""
          echo "📊 Deployment Details:"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Project ID: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo ""
          echo "🔍 Troubleshooting:"
          echo "  • Check workflow logs for detailed error information"
          echo "  • Verify ArgoCD cluster connectivity"
          echo "  • Ensure ENABLE_AUTO_DEPLOY repository variable is set to 'true'"
          echo "  • Check generated manifest files for syntax errors"
          echo ""
          echo "🛠️ Manual Deployment:"
          echo "  kubectl apply -f deployments/${{ needs.validate-dispatch.outputs.project-id }}/argocd/project.yaml"
          echo "  kubectl apply -f deployments/${{ needs.validate-dispatch.outputs.project-id }}/overlays/${{ needs.validate-dispatch.outputs.environment }}/application.yaml"

  # GitOps Promotion Workflow - Automatic staging deployment after dev
  promote-to-staging:
    needs: [validate-dispatch, deploy-to-argocd]
    if: needs.deploy-to-argocd.outputs.deployment-success == 'true' && needs.deploy-to-argocd.outputs.environment == 'dev'
    runs-on: [self-hosted, Linux]
    # Removed manual approval - automatic deployment
    outputs:
      promotion-triggered: ${{ steps.promote.outputs.triggered }}
      staging-payload: ${{ steps.promote.outputs.staging-payload }}
    steps:
      - name: 🎯 Prepare Staging Promotion
        id: promote
        run: |
          echo "🚀 Preparing promotion from dev to staging..."
          echo "📋 Original deployment details:"
          echo "  • Project: ${{ needs.deploy-to-argocd.outputs.project-id }}"
          echo "  • Application: ${{ needs.deploy-to-argocd.outputs.app-name }}"
          echo "  • Current Environment: ${{ needs.deploy-to-argocd.outputs.environment }}"
          echo "  • Current Docker Tag: ${{ needs.deploy-to-argocd.outputs.docker-tag }}"
          echo ""
          echo "🚀 Automatic promotion to staging (no approval required)"
          echo "⚡ Staging deployment will start immediately"
          echo ""

          # Create staging database secrets JSON
          echo "🔐 Preparing staging database configuration..."
          STAGING_DB_SECRETS=$(cat << EOF | jq -c .
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET_SPRING }}",
            "ENABLE_DATABASE" : "${{ secrets.ENABLE_DATABASE_SPRING }}",
            "DB_HOST": "${{ secrets.DB_HOST_SPRING_STAGING }}",
            "DB_USER": "${{ secrets.DB_USER_SPRING_STAGING }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD_SPRING_STAGING }}",
            "DB_NAME": "${{ secrets.DB_NAME_SPRING_STAGING }}",
            "DB_PORT": "${{ secrets.DB_PORT_SPRING_STAGING }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_SPRING_STAGING }}",
            "SMTP_USER": "${{ secrets.SMTP_USER_SPRING }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS_SPRING }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_SPRING }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
          }
          EOF
          )

          # Base64 encode the staging database secrets
          STAGING_SECRETS_ENCODED=$(echo "$STAGING_DB_SECRETS" | base64 -w 0)

          echo "📋 Staging database configuration prepared"
          echo "  • JWT_SECRET: ${{ secrets.JWT_SECRET_SPRING }}"
          echo "  • ENABLE_DATABASE: ${{ secrets.ENABLE_DATABASE_SPRING }}"
          echo "  • DB_HOST: ${{ secrets.DB_HOST_SPRING_STAGING }}"
          echo "  • DB_USER: ${{ secrets.DB_USER_SPRING_STAGING }}"
          echo "  • DB_NAME: ${{ secrets.DB_NAME_SPRING_STAGING }}"
          echo "  • DB_PORT: ${{ secrets.DB_PORT_SPRING }}"
          echo "  • DB_SSL_MODE: ${{ secrets.DB_SSL_MODE_SPRING }}"
          echo "  • SMTP_USER: ${{ secrets.SMTP_USER_SPRING }}"
          echo "  • SMTP_PASS: ${{ secrets.SMTP_PASS_SPRING }}"
          echo "  • GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID_SPRING }}"
          echo "  • GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
          echo "  • Secrets encoded length: ${#STAGING_SECRETS_ENCODED}"
          echo ""

          # Create staging promotion payload with environment-specific Docker tag and database secrets
          STAGING_PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "${{ needs.deploy-to-argocd.outputs.app-name }}",
            "project_id": "${{ needs.deploy-to-argocd.outputs.project-id }}",
            "application_type": "${{ needs.deploy-to-argocd.outputs.application-type }}",
            "environment": "staging",
            "docker_image": "${{ needs.deploy-to-argocd.outputs.docker-image }}",
            "docker_tag": "staging",
            "source_repo": "${{ needs.deploy-to-argocd.outputs.source-repo }}",
            "source_branch": "${{ needs.deploy-to-argocd.outputs.source-branch }}",
            "commit_sha": "${{ needs.deploy-to-argocd.outputs.commit-sha }}",
            "secrets_encoded": "$STAGING_SECRETS_ENCODED"
          }
          EOF
          )

          echo "🎯 Staging promotion payload:"
          echo "$STAGING_PAYLOAD" | jq .

          echo "triggered=true" >> $GITHUB_OUTPUT
          echo "staging-payload=$STAGING_PAYLOAD" >> $GITHUB_OUTPUT

          echo "✅ Staging promotion approved! Proceeding with deployment..."

  promote-docker-image-staging:
    needs: [promote-to-staging]
    runs-on: [self-hosted, Linux]
    outputs:
      image-promotion-success: ${{ steps.promote-image.outputs.success }}
      promoted-image: ${{ steps.promote-image.outputs.promoted-image }}
    steps:
      - name: 🐳 Promote Docker Image to Staging
        id: promote-image
        run: |
          echo "🐳 Promoting Docker image for staging deployment..."

          SOURCE_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          TARGET_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}:staging"

          echo "📋 Image promotion details:"
          echo "  • Source Image: $SOURCE_IMAGE"
          echo "  • Target Image: $TARGET_IMAGE"
          echo "  • Registry: DigitalOcean Container Registry (DOCR)"
          echo ""

          # Install doctl if not available
          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz
            tar xf doctl-1.104.0-linux-amd64.tar.gz
            sudo mv doctl /usr/local/bin
          fi

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"

          # Login to DOCR
          echo "🔐 Logging into DigitalOcean Container Registry..."
          doctl registry login

          # Pull the source image
          echo "📥 Pulling source image: $SOURCE_IMAGE"
          if ! docker pull "$SOURCE_IMAGE"; then
            echo "❌ Failed to pull source image: $SOURCE_IMAGE"
            echo "This might be because the image doesn't exist or there are authentication issues."
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Tag the image for staging
          echo "🏷️ Tagging image for staging: $TARGET_IMAGE"
          docker tag "$SOURCE_IMAGE" "$TARGET_IMAGE"

          # Push the staging image
          echo "📤 Pushing staging image: $TARGET_IMAGE"
          if docker push "$TARGET_IMAGE"; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "promoted-image=$TARGET_IMAGE" >> $GITHUB_OUTPUT
            echo "✅ Successfully promoted image to staging: $TARGET_IMAGE"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to push staging image: $TARGET_IMAGE"
            exit 1
          fi

          # Clean up local images to save space
          echo "🧹 Cleaning up local images..."
          docker rmi "$SOURCE_IMAGE" "$TARGET_IMAGE" || true

  deploy-to-staging:
    needs: [promote-to-staging, promote-docker-image-staging]
    runs-on: [self-hosted, Linux]
    outputs:
      staging-deployment-success: ${{ steps.trigger.outputs.success }}
    steps:
      - name: 🚀 Trigger Staging Deployment
        id: trigger
        run: |
          echo "🚀 Triggering staging deployment via repository dispatch..."
          echo "🐳 Using promoted image: ${{ needs.promote-docker-image-staging.outputs.promoted-image }}"

          STAGING_PAYLOAD='${{ needs.promote-to-staging.outputs.staging-payload }}'

          # Trigger repository dispatch for staging deployment
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITOPS_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            https://api.github.com/repos/${{ github.repository }}/dispatches \
            -d "{
              \"event_type\": \"deploy-to-argocd\",
              \"client_payload\": $STAGING_PAYLOAD
            }"

          if [ $? -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Staging deployment triggered successfully!"
            echo ""
            echo "📊 Staging Deployment Details:"
            echo "$STAGING_PAYLOAD" | jq .
            echo ""
            echo "🔗 Monitor the staging deployment in the Actions tab"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to trigger staging deployment"
            exit 1
          fi

  # Production promotion - Automatic after staging deployment success
  promote-to-production:
    needs: [deploy-to-staging]
    runs-on: [self-hosted, Linux]
    outputs:
      promotion-triggered: ${{ steps.promote.outputs.triggered }}
      production-payload: ${{ steps.promote.outputs.production-payload }}
    steps:
      - name: 🎯 Automatic Production Promotion
        id: promote
        run: |
          echo "🚀 Automatically promoting from staging to production..."
          echo "📋 Staging deployment completed successfully"
          echo "  • Project: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Source Environment: staging"
          echo "  • Target Environment: production"
          echo ""
          echo "🚀 Automatic promotion to production (no approval required)"
          echo "⚡ Production deployment will start immediately"
          echo ""

          # Create production database secrets JSON
          echo "🔐 Preparing production database configuration..."
          PRODUCTION_DB_SECRETS=$(cat << EOF | jq -c .
          {
            "JWT_SECRET": "${{ secrets.JWT_SECRET_SPRING }}",
            "ENABLE_DATABASE" : "${{ secrets.ENABLE_DATABASE_SPRING }}",
            "DB_HOST": "${{ secrets.DB_HOST_SPRING_PROD }}",
            "DB_USER": "${{ secrets.DB_USER_SPRING_PROD }}",
            "DB_PASSWORD": "${{ secrets.DB_PASSWORD_SPRING_PROD }}",
            "DB_NAME": "${{ secrets.DB_NAME_SPRING_PROD }}",
            "DB_PORT": "${{ secrets.DB_PORT_SPRING }}",
            "DB_SSL_MODE": "${{ secrets.DB_SSL_MODE_SPRING }}",
            "SMTP_USER": "${{ secrets.SMTP_USER_SPRING }}",
            "SMTP_PASS": "${{ secrets.SMTP_PASS_SPRING }}",
            "GOOGLE_CLIENT_ID": "${{ secrets.GOOGLE_CLIENT_ID_SPRING }}",
            "GOOGLE_CLIENT_SECRET": "${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
          }
          EOF
          )

          # Base64 encode the production database secrets
          PRODUCTION_SECRETS_ENCODED=$(echo "$PRODUCTION_DB_SECRETS" | base64 -w 0)

          echo "📋 Production database configuration prepared"
          echo "  • JWT_SECRET: ${{ secrets.JWT_SECRET_SPRING }}"
          echo "  • ENABLE_DATABASE: ${{ secrets.ENABLE_DATABASE_SPRING }}"
          echo "  • DB_HOST: ${{ secrets.DB_HOST_SPRING_PRODUCTION }}"
          echo "  • DB_USER: ${{ secrets.DB_USER_SPRING_PRODUCTION }}"
          echo "  • DB_NAME: ${{ secrets.DB_NAME_SPRING_PRODUCTION }}"
          echo "  • DB_PORT: ${{ secrets.DB_PORT_SPRING_PRODUCTION }}"
          echo "  • DB_SSL_MODE: ${{ secrets.DB_SSL_MODE_SPRING_PRODUCTION }}"
          echo "  • SMTP_USER: ${{ secrets.SMTP_USER_SPRING }}"
          echo "  • SMTP_PASS: ${{ secrets.SMTP_PASS_SPRING }}"
          echo "  • GOOGLE_CLIENT_ID: ${{ secrets.GOOGLE_CLIENT_ID_SPRING }}"
          echo "  • GOOGLE_CLIENT_SECRET: ${{ secrets.GOOGLE_CLIENT_SECRET_SPRING }}"
          echo "  • Secrets encoded length: ${#PRODUCTION_SECRETS_ENCODED}"
          echo ""

          # Create production promotion payload with environment-specific Docker tag and database secrets
          PRODUCTION_PAYLOAD=$(cat << EOF | jq -c .
          {
            "app_name": "${{ needs.deploy-to-argocd.outputs.app-name }}",
            "project_id": "${{ needs.deploy-to-argocd.outputs.project-id }}",
            "application_type": "${{ needs.deploy-to-argocd.outputs.application-type }}",
            "environment": "production",
            "docker_image": "${{ needs.deploy-to-argocd.outputs.docker-image }}",
            "docker_tag": "production",
            "source_repo": "${{ needs.deploy-to-argocd.outputs.source-repo }}",
            "source_branch": "${{ needs.deploy-to-argocd.outputs.source-branch }}",
            "commit_sha": "${{ needs.deploy-to-argocd.outputs.commit-sha }}",
            "secrets_encoded": "$PRODUCTION_SECRETS_ENCODED"
          }
          EOF
          )

          echo "🎯 Production promotion payload:"
          echo "$PRODUCTION_PAYLOAD" | jq .

          echo "triggered=true" >> $GITHUB_OUTPUT
          echo "production-payload=$PRODUCTION_PAYLOAD" >> $GITHUB_OUTPUT

          echo "✅ Production promotion approved! Proceeding with deployment..."

  promote-docker-image-production:
    needs: [promote-to-production]
    runs-on: [self-hosted, Linux]
    outputs:
      image-promotion-success: ${{ steps.promote-image.outputs.success }}
      promoted-image: ${{ steps.promote-image.outputs.promoted-image }}
    steps:
      - name: 🐳 Promote Docker Image to Production
        id: promote-image
        run: |
          echo "🐳 Promoting Docker image for production deployment..."

          # For production, we promote from staging tag to production tag
          SOURCE_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}:staging"
          TARGET_IMAGE="${{ needs.validate-dispatch.outputs.docker-image }}:production"

          echo "📋 Image promotion details:"
          echo "  • Source Image: $SOURCE_IMAGE"
          echo "  • Target Image: $TARGET_IMAGE"
          echo "  • Registry: DigitalOcean Container Registry (DOCR)"
          echo ""

          # Install doctl if not available
          if ! command -v doctl &> /dev/null; then
            echo "📦 Installing doctl..."
            cd /tmp
            wget https://github.com/digitalocean/doctl/releases/download/v1.104.0/doctl-1.104.0-linux-amd64.tar.gz
            tar xf doctl-1.104.0-linux-amd64.tar.gz
            sudo mv doctl /usr/local/bin
          fi

          # Authenticate with DigitalOcean
          echo "🔐 Authenticating with DigitalOcean..."
          doctl auth init --access-token "${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}"

          # Login to DOCR
          echo "🔐 Logging into DigitalOcean Container Registry..."
          doctl registry login

          # Pull the staging image
          echo "📥 Pulling staging image: $SOURCE_IMAGE"
          if ! docker pull "$SOURCE_IMAGE"; then
            echo "❌ Failed to pull staging image: $SOURCE_IMAGE"
            echo "This might be because the staging image doesn't exist or there are authentication issues."
            echo "success=false" >> $GITHUB_OUTPUT
            exit 1
          fi

          # Tag the image for production
          echo "🏷️ Tagging image for production: $TARGET_IMAGE"
          docker tag "$SOURCE_IMAGE" "$TARGET_IMAGE"

          # Push the production image
          echo "📤 Pushing production image: $TARGET_IMAGE"
          if docker push "$TARGET_IMAGE"; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "promoted-image=$TARGET_IMAGE" >> $GITHUB_OUTPUT
            echo "✅ Successfully promoted image to production: $TARGET_IMAGE"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to push production image: $TARGET_IMAGE"
            exit 1
          fi

          # Clean up local images to save space
          echo "🧹 Cleaning up local images..."
          docker rmi "$SOURCE_IMAGE" "$TARGET_IMAGE" || true

  deploy-to-production:
    needs: [promote-to-production, promote-docker-image-production]
    runs-on: [self-hosted, Linux]
    outputs:
      production-deployment-success: ${{ steps.trigger.outputs.success }}
    steps:
      - name: 🚀 Trigger Production Deployment
        id: trigger
        run: |
          echo "🚀 Triggering production deployment via repository dispatch..."
          echo "🐳 Using promoted image: ${{ needs.promote-docker-image-production.outputs.promoted-image }}"

          PRODUCTION_PAYLOAD='${{ needs.promote-to-production.outputs.production-payload }}'

          # Trigger repository dispatch for production deployment
          curl -X POST \
            -H "Authorization: token ${{ secrets.GITOPS_TOKEN }}" \
            -H "Accept: application/vnd.github.v3+json" \
            -H "Content-Type: application/json" \
            https://api.github.com/repos/${{ github.repository }}/dispatches \
            -d "{
              \"event_type\": \"deploy-to-argocd\",
              \"client_payload\": $PRODUCTION_PAYLOAD
            }"

          if [ $? -eq 0 ]; then
            echo "success=true" >> $GITHUB_OUTPUT
            echo "✅ Production deployment triggered successfully!"
            echo ""
            echo "📊 Production Deployment Details:"
            echo "$PRODUCTION_PAYLOAD" | jq .
            echo ""
            echo "🔗 Monitor the production deployment in the Actions tab"
          else
            echo "success=false" >> $GITHUB_OUTPUT
            echo "❌ Failed to trigger production deployment"
            exit 1
          fi

  # Summary and Notification Job
  deployment-summary:
    needs: [deploy-to-argocd, deploy-to-staging, deploy-to-production]
    if: always()
    runs-on: [self-hosted, Linux]
    steps:
      - name: 📊 Deployment and Promotion Summary
        run: |
          echo "🎉 GitOps Deployment and Promotion Workflow Summary"
          echo "=================================================="
          echo ""
          echo "📋 Original Deployment:"
          echo "  • Project: ${{ needs.validate-dispatch.outputs.project-id }}"
          echo "  • Application: ${{ needs.validate-dispatch.outputs.app-name }}"
          echo "  • Environment: ${{ needs.validate-dispatch.outputs.environment }}"
          echo "  • Docker Image: ${{ needs.validate-dispatch.outputs.docker-image }}:${{ needs.validate-dispatch.outputs.docker-tag }}"
          echo "  • Application Type: ${{ needs.validate-dispatch.outputs.application-type }}"
          echo "  • Source Repository: ${{ needs.validate-dispatch.outputs.source-repo }}"
          echo "  • Source Branch: ${{ needs.validate-dispatch.outputs.source-branch }}"
          echo "  • Commit SHA: ${{ needs.validate-dispatch.outputs.commit-sha }}"
          echo ""

          # Check deployment status
          if [ "${{ needs.deploy-to-argocd.outputs.deployment-success }}" = "true" ]; then
            echo "✅ Initial deployment to ${{ needs.validate-dispatch.outputs.environment }} completed successfully"
          else
            echo "❌ Initial deployment to ${{ needs.validate-dispatch.outputs.environment }} failed"
          fi

          # Check staging promotion status (only for dev deployments)
          if [ "${{ needs.validate-dispatch.outputs.environment }}" = "dev" ]; then
            if [ "${{ needs.promote-to-staging.outputs.promotion-triggered }}" = "true" ]; then
              echo "✅ Staging promotion approved and triggered"

              # Check image promotion status
              if [ "${{ needs.promote-docker-image-staging.outputs.image-promotion-success }}" = "true" ]; then
                echo "✅ Docker image promoted to staging: ${{ needs.promote-docker-image-staging.outputs.promoted-image }}"
              else
                echo "❌ Docker image promotion to staging failed"
              fi

              # Check deployment trigger status
              if [ "${{ needs.deploy-to-staging.outputs.staging-deployment-success }}" = "true" ]; then
                echo "✅ Staging deployment triggered successfully"
                echo "   🎯 Staging will use Docker tag: staging"
                echo "   📋 Monitor staging deployment in a separate workflow run"
              else
                echo "❌ Staging deployment trigger failed"
              fi
            else
              echo "⏳ Staging promotion will proceed automatically"
              echo "    No manual approval required - automatic deployment enabled"
            fi
          fi

          # Check production promotion status (only for staging deployments)
          if [ "${{ needs.validate-dispatch.outputs.environment }}" = "staging" ]; then
            if [ "${{ needs.promote-to-production.outputs.promotion-triggered }}" = "true" ]; then
              echo "✅ Production promotion approved and triggered"

              # Check image promotion status
              if [ "${{ needs.promote-docker-image-production.outputs.image-promotion-success }}" = "true" ]; then
                echo "✅ Docker image promoted to production: ${{ needs.promote-docker-image-production.outputs.promoted-image }}"
              else
                echo "❌ Docker image promotion to production failed"
              fi

              # Check deployment trigger status
              if [ "${{ needs.deploy-to-production.outputs.production-deployment-success }}" = "true" ]; then
                echo "✅ Production deployment triggered successfully"
                echo "   🎯 Production will use Docker tag: production"
                echo "   📋 Monitor production deployment in a separate workflow run"
              else
                echo "❌ Production deployment trigger failed"
              fi
            else
              echo "⏳ Production promotion will proceed automatically"
              echo "    No manual approval required - automatic deployment enabled"
            fi
          fi

          # Show next steps based on current environment
          echo ""
          echo "🔗 Next Steps:"
          if [ "${{ needs.validate-dispatch.outputs.environment }}" = "dev" ]; then
            echo "  • Monitor dev deployment in ArgoCD dashboard"
            echo "  • Test the application in dev environment"
            echo "  • Staging promotion will start automatically after dev success"
          elif [ "${{ needs.validate-dispatch.outputs.environment }}" = "staging" ]; then
            echo "  • Monitor staging deployment in ArgoCD dashboard"
            echo "  • Run staging tests and validation"
            echo "  • Production promotion will start automatically after staging success"
          elif [ "${{ needs.validate-dispatch.outputs.environment }}" = "production" ]; then
            echo "  • Monitor production deployment in ArgoCD dashboard"
            echo "  • Verify application health in production"
            echo "  • Run production smoke tests"
          fi

          echo ""
          echo "🔗 Next Steps:"
          echo "  • Monitor deployment progress in ArgoCD dashboard"
          echo "  • Check application health in target environment(s)"
          echo "  • Review automatic promotion workflows in GitHub Actions"
          echo "  • All promotions happen automatically - no manual approval needed"
          echo ""
          echo "📚 Environment-Specific Docker Tags:"
          echo "  • dev environment uses: latest tag"
          echo "  • staging environment uses: staging tag"
          echo "  • production environment uses: production tag"
          echo ""
          echo "🔄 Deployment Flow:"
          echo "  1. dev (automatic) → 2. staging (manual approval) → 3. production (manual approval)"
          echo ""
          echo "⚠️ Important Notes:"
          echo "  • Staging can only be deployed after successful dev deployment"
          echo "  • Production can only be deployed after successful staging deployment"
          echo "  • Each promotion requires manual approval in GitHub Actions"
          echo "  • Environment-specific Docker tags are automatically applied"
